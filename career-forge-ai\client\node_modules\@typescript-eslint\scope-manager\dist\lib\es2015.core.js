"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib @typescript-eslint/repo-tools
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2015_core = void 0;
const base_config_1 = require("./base-config");
exports.es2015_core = {
    Array: base_config_1.TYPE,
    ArrayConstructor: base_config_1.TYPE,
    DateConstructor: base_config_1.TYPE,
    Function: base_config_1.TYPE,
    Math: base_config_1.TYPE,
    NumberConstructor: base_config_1.TYPE,
    ObjectConstructor: base_config_1.TYPE,
    ReadonlyArray: base_config_1.TYPE,
    RegExp: base_config_1.TYPE,
    RegExpConstructor: base_config_1.TYPE,
    String: base_config_1.TYPE,
    StringConstructor: base_config_1.TYPE,
};
//# sourceMappingURL=es2015.core.js.map